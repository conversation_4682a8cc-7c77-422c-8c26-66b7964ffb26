document.addEventListener("DOMContentLoaded", async () => {
  // --- Selectors (existing + new) ---
  const loading = document.getElementById("loading");
  const results = document.getElementById("results");
  const errorBox = document.getElementById("error");
  const historyBox = document.getElementById("history");
  const historyList = document.getElementById("history-list");

  const verdictText = document.getElementById("verdict-text").querySelector("span");
  const confidenceLabel = document.getElementById("confidence-label");
  const progressCircle = document.querySelector(".progress-ring__circle");

  const explainToggle = document.getElementById("explain-toggle");
  const explanation = document.getElementById("explanation");
  const retryBtn = document.getElementById("retry-btn");
  const darkToggle = document.getElementById("dark-toggle");
  const historyBtn = document.getElementById("history-btn");

  const typeBadge = document.getElementById("type-badge");

  // --- NEW: tabs & quiz elements ---
  const tabButtons = Array.from(document.querySelectorAll(".tab-btn"));
  const tabSections = Array.from(document.querySelectorAll(".tab-section"));

  // Quiz elements
  const qIndexEl = document.getElementById("q-index");
  const qScoreEl = document.getElementById("q-score");
  const qPrompt = document.getElementById("q-prompt");
  const qOptions = document.getElementById("q-options");
  const checkBtn = document.getElementById("check-btn");
  const nextBtn = document.getElementById("next-btn");
  const qFeedback = document.getElementById("q-feedback");
  const progressDots = document.getElementById("progress-dots");

  // Leaderboard & profile
  const leaderboardList = document.getElementById("leaderboard-list");
  const clearLeaderboardBtn = document.getElementById("clear-leaderboard");
  const profileName = document.getElementById("profile-name");
  const profileAvatar = document.getElementById("profile-avatar");
  const profileOptin = document.getElementById("profile-optin");
  const saveProfileBtn = document.getElementById("save-profile");
  const clearDataBtn = document.getElementById("clear-data");
  const statAttempts = document.getElementById("stat-attempts");
  const statBest = document.getElementById("stat-best");
  const statStreak = document.getElementById("stat-streak");

  // --- NEW: internal state ---
  let activeTab = "verify";
  let pendingType = "image";
  let pendingValue = null;

  // Quiz state
  let quizPool = []; // will be loaded from local JS pool
  let session = {
    qIds: [],
    answers: [null, null, null],
    checked: [false, false, false],
    score: 0,
    finished: false
  };
  let currentIndex = 0;

  // Profile + leaderboard
  let profile = { nickname: "", avatar: "🙂", leaderboardOptIn: true, stats: { attempts: 0, best: 0, streak: 0 } };
  let leaderboard = [];

  // --- Load lastCheck (existing behavior) ---
  try {
    const res = await chrome.storage.local.get("lastCheck");
    const lastCheck = res?.lastCheck;
    if (lastCheck?.type && (lastCheck.data || lastCheck.value)) {
      pendingType = lastCheck.type;
      pendingValue = lastCheck.data || lastCheck.value;
    }
  } catch (e) {
    // ignore
  }

  // ------------------------------
  // Small local quiz pool (dummy)
  // ------------------------------
  quizPool = [
    {
      id: "q1",
      prompt: "Which international agency reported on global food prices in 2024?",
      options: ["UNICEF", "FAO", "WHO", "IMF"],
      correctIndex: 1,
      explanation: "FAO (Food and Agriculture Organization) publishes regular food price indexes."
    },
    {
      id: "q2",
      prompt: "Which country hosted the 2023 UN Climate Change Conference (COP)?",
      options: ["Egypt", "United Arab Emirates", "Brazil", "India"],
      correctIndex: 0,
      explanation: "COP27 was held in Sharm El-Sheikh, Egypt in 2023."
    },
    {
      id: "q3",
      prompt: "What does 'fake news' primarily refer to?",
      options: ["Satire only", "Deliberately false or misleading information", "Old news", "Editorial opinion"],
      correctIndex: 1,
      explanation: "Fake news usually denotes deliberately false or misleading information presented as news."
    },
    {
      id: "q4",
      prompt: "Which platform introduced fact-check labels widely to combat misinformation?",
      options: ["LinkedIn", "TikTok", "Facebook", "Pinterest"],
      correctIndex: 2,
      explanation: "Facebook (Meta) rolled out prominent fact-checking partnerships and labels."
    },
    {
      id: "q5",
      prompt: "Which organization runs the global fact-checking network 'IFCN'?",
      options: ["Poynter Institute", "Reuters", "AP", "BBC"],
      correctIndex: 0,
      explanation: "The International Fact-Checking Network is a unit of the Poynter Institute."
    }
  ];

  // ------------------------------
  // Storage helpers (wrap chrome.storage)
  // ------------------------------
  function localSet(key, value) {
    return new Promise((resolve) => {
      try {
        chrome.storage.local.set({ [key]: value }, () => resolve());
      } catch (e) { resolve(); }
    });
  }
  function localGet(key) {
    return new Promise((resolve) => {
      try {
        chrome.storage.local.get(key, (res) => resolve(res?.[key]));
      } catch (e) { resolve(undefined); }
    });
  }
  function syncSet(key, value) {
    return new Promise((resolve) => {
      try {
        chrome.storage.sync.set({ [key]: value }, () => resolve());
      } catch (e) { resolve(); }
    });
  }
  function syncGet(key) {
    return new Promise((resolve) => {
      try {
        chrome.storage.sync.get(key, (res) => resolve(res?.[key]));
      } catch (e) { resolve(undefined); }
    });
  }

  // ------------------------------
  // Initialize profile & leaderboard from storage
  // ------------------------------
  async function loadProfileAndLeaderboard() {
    const p = await syncGet("vi_profile");
    if (p) profile = { ...profile, ...p };
    const lb = await localGet("vi_leaderboard");
    if (Array.isArray(lb)) leaderboard = lb;
    renderProfile();
    renderLeaderboard();
  }

  // ------------------------------
  // Tab switching
  // ------------------------------
  function switchTab(tab) {
    activeTab = tab;
    tabButtons.forEach(btn => btn.classList.toggle("active", btn.dataset.tab === tab));
    tabSections.forEach(s => s.classList.toggle("hidden", s.id !== tab));

    // refresh render on show
    if (tab === "verify") {
      if (!session.qIds.length) startSession();
      renderQuestion(currentIndex);
    } else if (tab === "leaderboard") {
      renderLeaderboard();
    } else if (tab === "profile") {
      renderProfile();
    }
  }
  tabButtons.forEach(btn => btn.addEventListener("click", () => switchTab(btn.dataset.tab)));

  // ------------------------------
  // Quiz: pick three random distinct questions
  // ------------------------------
  function pickThreeQuestions() {
    const shuffled = [...quizPool].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 3).map(q => q.id);
  }

  function startSession() {
    session.qIds = pickThreeQuestions();
    session.answers = [null, null, null];
    session.checked = [false, false, false];
    session.score = 0;
    session.finished = false;
    currentIndex = 0;
    updateProgressDots();
    saveSession();
  }

  async function saveSession() {
    await localSet("vi_quizSession", session);
  }

  async function loadSession() {
    const s = await localGet("vi_quizSession");
    if (s) session = s;
    return session;
  }

  function getQuestionByIndex(i) {
    const qId = session.qIds[i];
    return quizPool.find(q => q.id === qId);
  }

  // Render one question
  function renderQuestion(i) {
    const q = getQuestionByIndex(i);
    if (!q) return;
    qIndexEl.textContent = i + 1;
    qScoreEl.textContent = session.score;
    qPrompt.textContent = q.prompt;
    qOptions.innerHTML = "";

    q.options.forEach((opt, idx) => {
      const li = document.createElement("li");
      li.textContent = opt;
      li.dataset.idx = idx;
      li.setAttribute("role", "option");
      if (session.answers[i] === idx) li.classList.add("selected");
      li.addEventListener("click", () => selectOption(idx));
      qOptions.appendChild(li);
    });

    // Reset feedback & buttons depending on checked state
    qFeedback.textContent = "";
    if (session.checked[i]) {
      applyResultStyles(i);
      checkBtn.disabled = true;
      nextBtn.disabled = (i >= 2);
    } else {
      checkBtn.disabled = false;
      nextBtn.disabled = true;
    }
    updateProgressDots();
  }

  

  // select option for current
  function selectOption(idx) {
    if (session.checked[currentIndex]) return; // lock after checking
    session.answers[currentIndex] = idx;
    renderQuestion(currentIndex);
    saveSession();
  }

  // Check answer
  function handleCheck() {
    const sel = session.answers[currentIndex];
    if (sel === null) {
      flashHint("Pick an option and press Check.");
      return;
    }
    session.checked[currentIndex] = true;
    const q = getQuestionByIndex(currentIndex);
    const correct = sel === q.correctIndex;

    // scoring + UI
    if (correct) {
      session.score += 3;
      qFeedback.textContent = "Correct! " + (q.explanation || "");
    } else {
      qFeedback.textContent = "Incorrect. " + (q.explanation || "");
    }

    // If all three have been checked and all correct, grant bonus
    const allChecked = session.checked.every(Boolean);
    if (allChecked) {
      const allCorrect = session.qIds.every((_, idx) => {
        const qq = getQuestionByIndex(idx);
        return session.answers[idx] === qq.correctIndex;
      });
      if (allCorrect) {
        session.score += 3; // bonus
      }
      session.finished = true;
      session.completedAt = new Date().toISOString();
      // update profile stats (attempt)
      updateProfileStats(session.score);
      // Enable save to leaderboard (we'll use Save action via profile)
      // show final summary in feedback
      qFeedback.textContent += ` Final score: ${session.score}.`;
    }

    applyResultStyles(currentIndex);
    checkBtn.disabled = true;
    nextBtn.disabled = currentIndex >= 2;
    saveSession();
    qScoreEl.textContent = session.score;
  }

  function applyResultStyles(i) {
    const q = getQuestionByIndex(i);
    if (!q) return;
    Array.from(qOptions.children).forEach(li => {
      li.classList.remove("selected", "correct", "incorrect");
      const idx = Number(li.dataset.idx);
      if (session.answers[i] === idx) li.classList.add("selected");
      if (session.checked[i]) {
        if (idx === q.correctIndex) li.classList.add("correct");
        else if (session.answers[i] === idx && idx !== q.correctIndex) li.classList.add("incorrect");
      }
    });
  }

  function goNext() {
    if (currentIndex < 2) {
      currentIndex++;
      renderQuestion(currentIndex);
    }
  }

  // small helper: update dots
  function updateProgressDots() {
    const dots = Array.from(progressDots.querySelectorAll(".dot"));
    dots.forEach((d, i) => d.classList.toggle("active", i === currentIndex));
  }

  // feedback flash
  function flashHint(msg) {
    const prev = qFeedback.textContent;
    qFeedback.textContent = msg;
    setTimeout(() => qFeedback.textContent = prev, 1200);
  }

  // profile stats update
  function updateProfileStats(score) {
    profile.stats.attempts = (profile.stats.attempts || 0) + 1;
    profile.stats.best = Math.max(profile.stats.best || 0, score);
    // simple streak logic: increment if full marks (12), else reset
    if (score === 12) profile.stats.streak = (profile.stats.streak || 0) + 1;
    else profile.stats.streak = 0;
    saveProfileLocal();
    renderProfile();
  }

  // ------------------------------
  // Leaderboard functions
  // ------------------------------
  function renderLeaderboard() {
    leaderboardList.innerHTML = "";
    if (leaderboard.length === 0) {
      leaderboardList.innerHTML = "<li>No entries yet</li>";
      return;
    }
    // sort desc
    const sorted = [...leaderboard].sort((a, b) => b.score - a.score);
    sorted.forEach((entry, i) => {
      const li = document.createElement("li");
      li.innerHTML = `
        <div>
          <strong>${entry.avatar || "🙂"} ${entry.nickname || "Anonymous"}</strong>
          <div class="meta">${new Date(entry.timestamp).toLocaleString()}</div>
        </div>
        <div><strong>${entry.score}</strong></div>
      `;
      if (entry.nickname === profile.nickname) li.classList.add("you");
      leaderboardList.appendChild(li);
    });
  }

  async function saveLeaderboardEntry(score) {
    if (!profile.leaderboardOptIn || !profile.nickname) return false;
    const entry = { nickname: profile.nickname, avatar: profile.avatar, score, timestamp: new Date().toISOString() };
    leaderboard.unshift(entry);
    leaderboard = leaderboard.slice(0, 50); // keep recent 50
    await localSet("vi_leaderboard", leaderboard);
    renderLeaderboard();
    return true;
  }

  // ------------------------------
  // Profile save/load
  // ------------------------------
  function renderProfile() {
    profileName.value = profile.nickname || "";
    profileAvatar.value = profile.avatar || "";
    profileOptin.checked = profile.leaderboardOptIn !== false;
    statAttempts.textContent = profile.stats.attempts || 0;
    statBest.textContent = profile.stats.best || 0;
    statStreak.textContent = profile.stats.streak || 0;
  }

  async function saveProfileLocal() {
    profile.nickname = profileName.value.trim() || profile.nickname;
    profile.avatar = profileAvatar.value.trim() || profile.avatar;
    profile.leaderboardOptIn = profileOptin.checked;
    await syncSet("vi_profile", profile).catch(()=>{});
  }

  saveProfileBtn.addEventListener("click", async () => {
    await saveProfileLocal();
    renderProfile();
    alert("✅ Profile saved.");
  });

  clearDataBtn.addEventListener("click", async () => {
    await localSet("vi_quizSession", {});
    session = { qIds: [], answers: [null, null, null], checked: [false, false, false], score: 0, finished: false };
    startSession();
    renderQuestion(currentIndex);
    alert("✅ Quiz data cleared.");
  });

  clearLeaderboardBtn.addEventListener("click", async () => {
    if (!confirm("Clear local leaderboard?")) return;
    leaderboard = [];
    await localSet("vi_leaderboard", leaderboard);
    renderLeaderboard();
  });

  // ------------------------------
  // UI events for quiz
  // ------------------------------
  checkBtn.addEventListener("click", handleCheck);
  nextBtn.addEventListener("click", () => {
    goNext();
  });

  // allow arrow navigation & Enter
  document.addEventListener("keydown", (e) => {
    if (activeTab !== "verify") return;
    if (e.key === "ArrowRight") {
      if (currentIndex < 2) goNext();
    } else if (e.key === "ArrowLeft") {
      if (currentIndex > 0) { currentIndex--; renderQuestion(currentIndex); }
    } else if (e.key === "Enter") {
      handleCheck();
    }
  });

  // basic touch swipe handler (for the quiz card)
  (function attachSwipe() {
    const el = document.getElementById("quiz-slider");
    if (!el) return;
    let startX = 0;
    let dist = 0;
    const threshold = 40;
    el.addEventListener("touchstart", (e) => {
      startX = e.touches[0].clientX;
    }, { passive: true });
    el.addEventListener("touchmove", (e) => {
      dist = e.touches[0].clientX - startX;
    }, { passive: true });
    el.addEventListener("touchend", () => {
      if (dist < -threshold) { // left swipe
        if (currentIndex < 2) { currentIndex++; renderQuestion(currentIndex); }
      } else if (dist > threshold) { // right swipe
        if (currentIndex > 0) { currentIndex--; renderQuestion(currentIndex); }
      }
      dist = 0;
    }, { passive: true });
  })();

  // ------------------------------
  // Verify UI with Gemini API integration
  // ------------------------------

  // API configuration from config.js
  const API_KEY = CONFIG.API_KEY;
  const MODEL_ID = CONFIG.MODEL_ID;
  const VERTEX_AI_URL = CONFIG.VERTEX_AI_URL;

  async function callVertexAI(content, contentType, retryCount = 0) {
    const maxRetries = 3;

    try {
      const apiUrl = `${VERTEX_AI_URL}/${MODEL_ID}:streamGenerateContent?key=${API_KEY}`;
      const requestBody = createVertexAIRequest(content, contentType);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!generatedText) {
        throw new Error('No response text from Vertex AI');
      }

      // Parse JSON response
      const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }

      const result = JSON.parse(jsonMatch[0]);
      return result;

    } catch (error) {
      console.error(`Vertex AI call failed (attempt ${retryCount + 1}):`, error);

      if (retryCount < maxRetries) {
        console.log(`Retrying... (${retryCount + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
        return callVertexAI(content, contentType, retryCount + 1);
      }

      throw error;
    }
  }

  function createVertexAIRequest(content, contentType) {
    const prompt = `
You are a fact-checking AI assistant. Analyze the provided ${contentType} and return a structured JSON response.

IMPORTANT INSTRUCTIONS:
- If analyzing an image, first examine the image and extract/parse any text visible in the image
- Always verify that any source URLs you provide are valid and accessible before including them in your response
- Base your fact-checking on reliable, verifiable sources

First, classify the content type as one of: "meme", "incomplete_sentence", "not_news", or "news".

If the content is "news", provide a verdict as one of: "real_text", "real_image", "false_text", or "false_image".
If the content is not news, set verdict to "not_applicable".

Provide a confidence score (0-100) for your assessment based on:
- Quality and reliability of available sources
- Clarity of the evidence
- Consensus among fact-checking organizations

Give a clear explanation of your reasoning, including:
- What specific claims were verified
- What evidence supports or contradicts the claims
- Any context that affects the interpretation

Provide a credible fact-checking source with a valid URL and name. Ensure the URL is accessible and relevant, recheck and revisit the url, if not found, search for other source, else return "not_applicable".

Return ONLY a valid JSON object with this exact structure:
{
  "content_type": "meme|incomplete_sentence|not_news|news",
  "verdict": "real_text|real_image|false_text|false_image|not_applicable",
  "confidence": 85,
  "explanation": "Your detailed explanation here",
  "source": {
    "url": "https://example.com",
    "name": "Source Name"
  }
}

${contentType === 'text' ? `Text to analyze: ${content}` : ''}
`;

    // Create request body based on content type
    if (contentType === 'image') {
      // Note: Vertex AI fileData format requires gs:// URLs (Google Cloud Storage)
      // For regular image URLs, we'll include the URL in the text prompt for now
      // TODO: Implement image upload to GCS or use base64 encoding
      const imagePrompt = `${prompt}\n\nImage URL to analyze: ${content}`;

      return {
        contents: {
          role: "user",
          parts: [
            {
              text: imagePrompt
            }
          ]
        }
      };
    } else {
      // For text content
      return {
        contents: {
          role: "user",
          parts: [
            {
              text: prompt
            }
          ]
        }
      };
    }
  }

  async function fetchResult() {
    loading.classList.remove("hidden");
    results.classList.add("hidden");
    errorBox.classList.add("hidden");

    // Validate API key
    if (!API_KEY || API_KEY === 'YOUR_API_KEY_HERE') {
      console.error('API key not configured');
      loading.classList.add("hidden");
      errorBox.classList.remove("hidden");
      document.querySelector("#error p").textContent = "⚠️ API key not configured. Please check config.js";
      return;
    }

    try {
      const content = pendingValue || (pendingType === "text" ? "Sample selected text…" : "demo.jpg");

      if (!content || content.trim() === "") {
        throw new Error("No content to verify");
      }

      const vertexResult = await callVertexAI(content, pendingType);

      // Transform Vertex AI response to expected format
      const transformedResult = {
        type: pendingType,
        value: content,
        verdict: transformVertexVerdict(vertexResult.verdict, vertexResult.content_type),
        confidence: vertexResult.confidence,
        explanation: vertexResult.explanation,
        source: vertexResult.source
      };

      loading.classList.add("hidden");
      showResults(transformedResult);
      saveToHistory(transformedResult);
      renderHistory();

    } catch (error) {
      console.error('Verification failed:', error);
      loading.classList.add("hidden");
      errorBox.classList.remove("hidden");

      // Update error message based on error type
      const errorElement = document.querySelector("#error p");
      if (error.message.includes('API request failed')) {
        errorElement.textContent = "⚠️ API service unavailable. Please try again.";
      } else if (error.message.includes('No valid JSON')) {
        errorElement.textContent = "⚠️ Invalid response from AI service.";
      } else {
        errorElement.textContent = "⚠️ Could not verify right now. Please try again.";
      }
    }
  }

  function transformVertexVerdict(vertexVerdict, contentType) {
    // Transform Vertex AI response to UI format
    if (contentType !== "news") {
      return "Not News";
    }

    switch (vertexVerdict) {
      case "real_text":
      case "real_image":
        return "Real";
      case "false_text":
      case "false_image":
        return "Fake";
      default:
        return "Uncertain";
    }
  }

  // --- Show results ---
  function showResults(data) {
    results.classList.remove("hidden");
    verdictText.textContent = data.verdict;

    // Type badge
    typeBadge.textContent = data.type === "image" ? "🖼️ Image" : "✍️ Text";

    // Confidence ring
    let offset = 188 - (188 * data.confidence) / 100;
    progressCircle.style.strokeDashoffset = offset;
    progressCircle.style.stroke = data.verdict === "Fake" ? "#dc2626" : "#22c55e";
    confidenceLabel.textContent = `${data.confidence}%`;

    // Explanation + source
    document.getElementById("explain-text").textContent = data.explanation;
    document.getElementById("source-link").innerHTML =
      `<img src="https://www.google.com/s2/favicons?sz=32&domain=${new URL(data.source.url).hostname}" /> 
       <a href="${data.source.url}" target="_blank">${data.source.name}</a>`;
  }

  // --- History management ---
  function saveToHistory(entry) {
    let history = JSON.parse(localStorage.getItem("verifyHistory")) || [];
    const entryWithTime = { ...entry, time: new Date().toLocaleTimeString() };
    history.unshift(entryWithTime);
    history = history.slice(0, 8); // keep last 8
    localStorage.setItem("verifyHistory", JSON.stringify(history));
  }

  function renderHistory() {
    const history = JSON.parse(localStorage.getItem("verifyHistory")) || [];
    historyList.innerHTML = "";

    if (history.length === 0) {
      historyList.innerHTML = "<li>No checks yet</li>";
      return;
    }

    history.forEach(item => {
      const li = document.createElement("li");

      // Icon + preview + confidence color
      const icon = item.type === "image" ? "🖼️" : "✍️";
      const preview = item.type === "image"
        ? (item.value || "").split("/").pop().slice(0, 12)
        : (item.value || "").slice(0, 20);
      const confColor = item.confidence >= 80 ? "green"
                      : item.confidence >= 50 ? "orange"
                      : "red";

      li.innerHTML = `
        ${icon} <strong>${preview}</strong> 
        <span style="color:${confColor}; font-size:0.85em;">(${item.confidence}%)</span>
      `;
      li.dataset.historyItem = JSON.stringify(item);
      historyList.appendChild(li);
    });
  }

  // --- UI events (existing) ---
  explainToggle.addEventListener("click", () => {
    explanation.classList.toggle("hidden");
  });

  retryBtn.addEventListener("click", fetchResult);

  darkToggle.addEventListener("click", () => {
    document.body.classList.toggle("dark");
    if (document.body.classList.contains("dark")) {
      localStorage.setItem("darkMode", "true");
    } else {
      localStorage.removeItem("darkMode");
    }
  });

  historyBtn.addEventListener("click", () => {
    historyBox.classList.toggle("hidden");
    if (!historyBox.classList.contains("hidden")) {
      renderHistory();
    }
  });

  historyList.addEventListener("click", (e) => {
    if (e.target && e.target.closest("li")?.dataset.historyItem) {
      const itemData = JSON.parse(e.target.closest("li").dataset.historyItem);
      showResults(itemData);
      historyBox.classList.add("hidden");
    }
  });

  document.getElementById("clear-history-btn").addEventListener("click", () => {
    localStorage.removeItem("verifyHistory");
    renderHistory();
    alert("✅ History cleared successfully!");
  });

  // ------------------------------
  // Initial load
  // ------------------------------
  (async function init() {
    // dark mode persistence
    if (localStorage.getItem("darkMode") === "true") {
      document.body.classList.add("dark");
    }

    // load profile & leaderboard
    await loadProfileAndLeaderboard();

    // load session if exists
    await loadSession();
    if (!session.qIds || session.qIds.length === 0) startSession();

    // start with verify tab visible
    switchTab("verify");

    fetchResult();
  })();

  // Expose small public functions for saving final session to leaderboard (used by UI if needed)
  // We'll save automatically in profile if user opts in once session finished and they click Save Profile
  // But also provide a simple one-off save (callable in console) for testing:
  window.viSaveScoreToLeaderboard = async () => {
    if (!session.finished) {
      alert("Finish the 3-question quiz to save your score.");
      return;
    }
    const saved = await saveLeaderboardEntry(session.score);
    if (saved) alert("Score saved to local leaderboard.");
    else alert("Profile not configured or opted out. Go to Profile to enable.");
  };

  // Allow long-press save: clicking profile save will also attempt to store last score if finished
  saveProfileBtn.addEventListener("click", async () => {
    await saveProfileLocal();
    if (session.finished) {
      await saveLeaderboardEntry(session.score);
      renderLeaderboard();
    }
  });
});





