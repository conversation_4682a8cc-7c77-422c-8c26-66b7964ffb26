/* --- Existing styles (unchanged) --- */
body {
  font-family: "Segoe UI", Roboto, sans-serif;
  margin: 0;
  padding: 12px;
  width: 320px;
  background: #f9fafb;
  color: #111827;
  transition: background 0.3s, color 0.3s;
}

.header {
  text-align: center;
  margin-bottom: 12px;
}

.logo {
  width: 48px;
  height: 48px;
}

h1 {
  font-size: 16px;
  margin: 6px 0 0;
  color: #2563eb;
}

.card {
  background: white;
  border-radius: 14px;
  padding: 12px;
  margin-bottom: 10px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.08);
}

.verdict-card h2 {
  font-size: 18px;
  margin: 0;
}

.confidence-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  position: relative;
}

.progress-ring__bg {
  fill: transparent;
  stroke: #e5e7eb;
}

.progress-ring__circle {
  fill: transparent;
  stroke: #22c55e;
  stroke-dasharray: 188;
  stroke-dashoffset: 188;
  transition: stroke-dashoffset 0.6s ease, stroke 0.3s;
}

#confidence-label {
  position: absolute;
  font-weight: bold;
  font-size: 16px;
}

.collapsible .collapse-btn {
  background: none;
  border: none;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  color: #2563eb;
}

.hidden { display: none; }

.source-card a {
  margin-left: 6px;
  color: #2563eb;
  text-decoration: none;
}

.source-card img {
  vertical-align: middle;
}

.error {
  background: #fee2e2;
  border-left: 4px solid #dc2626;
  color: #b91c1c;
}

.footer {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.footer button {
  border: 1px solid #d1d5db;
  background: white;
  padding: 6px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.footer button:hover {
  background-color: #f3f4f6;
}

/* --- Dark Mode --- */
body.dark {
  background: #1f2937;
  color: #f9fafb;
}

body.dark .card {
  background: #374151;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

body.dark h1, body.dark .collapsible .collapse-btn, body.dark .source-card a {
  color: #60a5fa;
}

body.dark .progress-ring__bg {
  stroke: #4b5563;
}

body.dark .error {
  background: #5c2b2b;
  color: #fecaca;
}

body.dark .footer button {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

body.dark .footer button:hover {
  background-color: #6b7280;
}

.footer-btn {
  border: 1px solid #d1d5db;
  background: white;
  padding: 6px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  margin-top: 10px;
}

.footer-btn:hover {
  background-color: #f3f4f6;
}

body.dark .footer-btn {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

body.dark .footer-btn:hover {
  background-color: #6b7280;
}

/* --- Loading Skeleton --- */
.loading .skeleton {
  background-color: #e5e7eb;
  border-radius: 4px;
}
body.dark .loading .skeleton {
  background-color: #4b5563;
}
.skeleton-text { height: 20px; width: 80%; margin-bottom: 10px; }
.skeleton-bar { height: 40px; width: 100%; }

/* --- History --- */
#history-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 150px;
  overflow-y: auto;
}

#history-list li {
  padding: 8px;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s;
}
body.dark #history-list li {
  border-bottom-color: #4b5563;
}

#history-list li:hover {
  background-color: #f3f4f6;
}
body.dark #history-list li:hover {
  background-color: #4b5563;
}

#history-list li:last-child {
  border-bottom: none;
}

.clear-btn {
  margin-top: 10px;
  width: 100%;
  border: 1px solid #d1d5db;
  background: white;
  padding: 6px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-btn:hover {
  background-color: #f3f4f6;
}

body.dark .clear-btn {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

body.dark .clear-btn:hover {
  background-color: #6b7280;
}

/* --- NEW: Tabs --- */
.top-tabs {
  display: flex;
  gap: 6px;
  padding: 6px;
  align-items: center;
}
.tab-btn {
  flex: 1;
  padding: 6px 8px;
  border-radius: 8px;
  background: transparent;
  border: 1px solid transparent;
  font-weight: 600;
  cursor: pointer;
  font-size: 13px;
}
.tab-btn.active {
  background: #eef2ff;
  color: #1e3a8a;
  border-color: #c7d2fe;
}
body.dark .tab-btn.active {
  background: #1f2937;
  color: #bfdbfe;
  border-color: #374151;
}

/* --- NEW: Quiz styles --- */
.quiz-card { padding: 10px; border-radius: 12px; }
.quiz-header { display:flex; justify-content:space-between; align-items:center; margin-bottom:8px; }
.quiz-progress { font-weight:600; font-size:13px; }
.quiz-score { font-weight:700; }

.quiz-slider { overflow: hidden; position: relative; min-height: 140px; }
.q-card { transition: transform 0.25s ease; }
.q-prompt { font-weight:700; margin-bottom:8px; }
.q-options { list-style:none; padding:0; margin:0 0 8px 0; }
.q-options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.q-options-grid li {
  padding: 8px;
  border-radius: 8px;
  margin-bottom: 0;
  border: 1px solid #e6e7eb;
  cursor: pointer;
  transition: background-color 0.15s, border-color 0.15s;
  text-align: center;
  font-size: 12px;
}
.q-options-grid li:hover { background:#f3f4f6; color: #2563eb; }
.q-options-grid li.selected { border-color:#2563eb; background:#eef2ff; }
.q-options-grid li.correct { border-color:#16a34a; background:#ecfdf5;color: #22c55e; }
.q-options-grid li.incorrect { border-color:#ef4444; background:#fff1f2;color: #dc2626; }
body.dark .q-options-grid li { border-color: #4b5563; }
body.dark .q-options-grid li:hover { background: #4b5563; color: #60a5fa; }
body.dark .q-options-grid li.selected { border-color: #6b7280; background: #1f2937 !important; }
body.dark .q-options-grid li.correct { border-color: #16a34a; background: #1f2937; color: #ecfdf5; }
body.dark .q-options-grid li.incorrect { border-color: #ef4444; background: #1f2937; color: #fff1f2; }

.quiz-actions { display:flex; gap:8px; margin-bottom:8px; }
.btn { padding:8px 10px; border-radius:8px; border:1px solid #d1d5db; background:white; cursor:pointer; }
.btn.primary { background:#2563eb; color:white; border-color:transparent; }
.btn[disabled] { opacity:0.6; cursor:not-allowed; }

.q-feedback { font-size:13px; min-height:20px; }

.quiz-footer { display:flex; justify-content:space-between; align-items:center; margin-top:6px; }
.progress-dots { display:flex; gap:6px; align-items:center; }
.dot { width:8px; height:8px; border-radius:50%; background:#e5e7eb; display:inline-block; }
.dot.active { background:#2563eb; }

/* --- NEW: Leaderboard --- */
.leaderboard-list { padding: 0; margin: 0; list-style:none; }
.leaderboard-list li {
  display:flex; justify-content:space-between; padding:8px 6px; border-bottom:1px solid #e5e7eb;
}
body.dark .leaderboard-list li { border-bottom-color: #4b5563; }
.leaderboard-list li .meta { font-size:12px; color:#6b7280; }
.leaderboard-list li.you { background:#eef2ff; }

/* --- NEW: Profile --- */
label { display:block; margin-bottom:8px; font-size:13px; }
input[type="text"] { width:100%; padding:8px; border-radius:8px; border:1px solid #e6e7eb; }
.inline-toggle { display:flex; gap:8px; align-items:center; }
.profile-stats p { margin:6px 0; font-size:13px; }
.profile-actions { display:flex; gap:8px; margin-top:8px; justify-content:space-between; }

/* --- small responsive tweaks --- */
@media (max-width:360px) {
  body { width: 320px; padding:10px; }
  .tab-btn { font-size:12px; padding:5px; }
}


