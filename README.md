# Fake News Verifier Extension

A Chrome browser extension that helps users verify the authenticity of news content using Google's Gemini AI.

## Features

- **Real-time Verification**: Verify news text and images using AI-powered fact-checking
- **Context Menu Integration**: Right-click on images or selected text to verify
- **Floating Action Bubble**: Quick verification for selected text on any webpage
- **Gamification**: Quiz system with fact-checking questions and leaderboards
- **History Tracking**: Keep track of previous verifications
- **Dark/Light Mode**: Toggle between themes
- **Profile System**: Track your fact-checking stats and scores

## Setup Instructions

### 1. Get a Vertex AI API Key

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Vertex AI API
4. Go to APIs & Services > Credentials
5. Create an API Key
6. Copy the generated API key

### 2. Configure the Extension

1. Open the `config.js` file in the extension directory
2. Replace `'YOUR_GEMINI_API_KEY_HERE'` with your actual API key:

```javascript
const CONFIG = {
  API_KEY: 'your-actual-api-key-here',
  MODEL_ID: 'gemini-2.0-flash-exp',
  VERTEX_AI_URL: 'https://aiplatform.googleapis.com/v1/publishers/google/models'
};
```

### 3. Install the Extension

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked"
4. Select the extension directory
5. The extension should now appear in your extensions list

## How It Works

### Content Classification

The extension uses Gemini AI to classify content into:
- **meme**: Humorous or satirical content
- **incomplete_sentence**: Partial or fragmented text
- **not_news**: Non-news content
- **news**: Actual news content

### Verification Process

For news content, the AI provides:
- **Verdict**: `real_text`, `real_image`, `false_text`, or `false_image`
- **Confidence Score**: 0-100% confidence in the assessment
- **Explanation**: Detailed reasoning for the verdict
- **Fact-check Source**: Credible source with URL and name

### Usage

1. **Right-click Method**: Right-click on images or selected text and choose "Verify"
2. **Floating Bubble**: Select text on any webpage to see the verification bubble
3. **Extension Popup**: Click the extension icon to see results and access other features

## API Integration

The extension integrates with Google's Vertex AI (Gemini 2.0 Flash Experimental) for:
- Content analysis and classification
- Fact-checking and verification
- Source attribution
- Confidence scoring

## Error Handling

- Automatic retry mechanism (up to 3 attempts)
- Exponential backoff for failed requests
- Detailed error messages for different failure types
- Graceful fallback for API unavailability

## Privacy

- No user data is stored on external servers
- All verification history is stored locally
- API calls are made directly to Google's Gemini service
- No tracking or analytics

## Development

The extension is built with:
- Manifest V3 for Chrome extensions
- Vanilla JavaScript (no frameworks)
- Chrome Storage API for persistence
- Modern CSS with dark/light theme support

## Current Limitations

### Image Processing
- The current implementation sends image URLs as text to the AI for analysis
- For full image processing capabilities, images would need to be uploaded to Google Cloud Storage first
- Future versions may implement direct image upload or base64 encoding

## Troubleshooting

### "API key not configured" Error
- Check that you've updated `config.js` with your actual API key
- Ensure the API key is valid and has proper permissions
- Make sure the Vertex AI API is enabled in your Google Cloud project

### "API service unavailable" Error
- Check your internet connection
- Verify the API key is still valid
- Ensure your Google Cloud project has Vertex AI API enabled
- Try again after a few moments

### Extension Not Loading
- Ensure all files are in the same directory
- Check the Chrome developer console for errors
- Verify manifest.json is properly formatted

## License

This project is for educational and research purposes.
