<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vertex AI API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { border-color: #4CAF50; background-color: #f1f8e9; }
        .error { border-color: #f44336; background-color: #ffebee; }
        .loading { border-color: #2196F3; background-color: #e3f2fd; }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976D2; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status {
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Vertex AI API Integration Test</h1>
    
    <div class="test-section">
        <h3>Configuration Check</h3>
        <div id="config-status" class="status">Checking configuration...</div>
        <pre id="config-details"></pre>
    </div>

    <div class="test-section">
        <h3>API Connectivity Test</h3>
        <button id="test-api-btn" onclick="testAPI()">Test API Connection</button>
        <div id="api-status" class="status"></div>
        <pre id="api-response"></pre>
    </div>

    <div class="test-section">
        <h3>Text Analysis Test</h3>
        <textarea id="test-text" placeholder="Enter text to analyze..." rows="3" style="width: 100%; margin-bottom: 10px;">Breaking: Scientists discover new planet in our solar system</textarea>
        <button id="test-text-btn" onclick="testTextAnalysis()">Test Text Analysis</button>
        <div id="text-status" class="status"></div>
        <pre id="text-response"></pre>
    </div>

    <div class="test-section">
        <h3>Image Analysis Test</h3>
        <input type="text" id="test-image" placeholder="Enter image URL..." style="width: 100%; margin-bottom: 10px;" value="https://example.com/news-image.jpg">
        <button id="test-image-btn" onclick="testImageAnalysis()">Test Image Analysis</button>
        <div id="image-status" class="status"></div>
        <pre id="image-response"></pre>
    </div>

    <script src="config.js"></script>
    <script>
        // Check configuration on load
        window.addEventListener('DOMContentLoaded', checkConfiguration);

        function checkConfiguration() {
            const configStatus = document.getElementById('config-status');
            const configDetails = document.getElementById('config-details');
            
            if (typeof CONFIG === 'undefined') {
                configStatus.textContent = '❌ Configuration not loaded';
                configStatus.parentElement.className = 'test-section error';
                configDetails.textContent = 'CONFIG object not found. Check if config.js is loaded properly.';
                return;
            }

            const issues = [];
            
            if (!CONFIG.API_KEY || CONFIG.API_KEY === 'YOUR_API_KEY_HERE') {
                issues.push('API_KEY not configured');
            }
            
            if (!CONFIG.MODEL_ID) {
                issues.push('MODEL_ID not configured');
            }
            
            if (!CONFIG.VERTEX_AI_URL) {
                issues.push('VERTEX_AI_URL not configured');
            }

            if (issues.length > 0) {
                configStatus.textContent = '⚠️ Configuration issues found';
                configStatus.parentElement.className = 'test-section error';
                configDetails.textContent = 'Issues:\n' + issues.join('\n') + '\n\nCurrent config:\n' + JSON.stringify(CONFIG, null, 2);
            } else {
                configStatus.textContent = '✅ Configuration looks good';
                configStatus.parentElement.className = 'test-section success';
                configDetails.textContent = JSON.stringify(CONFIG, null, 2);
            }
        }

        async function testAPI() {
            const button = document.getElementById('test-api-btn');
            const status = document.getElementById('api-status');
            const response = document.getElementById('api-response');
            
            button.disabled = true;
            status.textContent = '🔄 Testing API connection...';
            status.parentElement.className = 'test-section loading';
            response.textContent = '';

            try {
                const apiUrl = `${CONFIG.VERTEX_AI_URL}/${CONFIG.MODEL_ID}:streamGenerateContent?key=${CONFIG.API_KEY}`;
                
                const testRequest = {
                    contents: {
                        role: "user",
                        parts: [
                            {
                                text: "Hello, please respond with 'API is working' to confirm connectivity."
                            }
                        ]
                    }
                };

                const apiResponse = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testRequest)
                });

                const responseText = await apiResponse.text();
                
                if (apiResponse.ok) {
                    status.textContent = '✅ API connection successful';
                    status.parentElement.className = 'test-section success';
                    response.textContent = `Status: ${apiResponse.status}\nResponse:\n${responseText}`;
                } else {
                    status.textContent = '❌ API connection failed';
                    status.parentElement.className = 'test-section error';
                    response.textContent = `Status: ${apiResponse.status}\nError:\n${responseText}`;
                }
            } catch (error) {
                status.textContent = '❌ API test failed';
                status.parentElement.className = 'test-section error';
                response.textContent = `Error: ${error.message}`;
            } finally {
                button.disabled = false;
            }
        }

        async function testTextAnalysis() {
            const button = document.getElementById('test-text-btn');
            const status = document.getElementById('text-status');
            const response = document.getElementById('text-response');
            const textInput = document.getElementById('test-text');
            
            button.disabled = true;
            status.textContent = '🔄 Analyzing text...';
            status.parentElement.className = 'test-section loading';
            response.textContent = '';

            try {
                const result = await callVertexAI(textInput.value, 'text');
                status.textContent = '✅ Text analysis completed';
                status.parentElement.className = 'test-section success';
                response.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                status.textContent = '❌ Text analysis failed';
                status.parentElement.className = 'test-section error';
                response.textContent = `Error: ${error.message}`;
            } finally {
                button.disabled = false;
            }
        }

        async function testImageAnalysis() {
            const button = document.getElementById('test-image-btn');
            const status = document.getElementById('image-status');
            const response = document.getElementById('image-response');
            const imageInput = document.getElementById('test-image');
            
            button.disabled = true;
            status.textContent = '🔄 Analyzing image...';
            status.parentElement.className = 'test-section loading';
            response.textContent = '';

            try {
                const result = await callVertexAI(imageInput.value, 'image');
                status.textContent = '✅ Image analysis completed';
                status.parentElement.className = 'test-section success';
                response.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                status.textContent = '❌ Image analysis failed';
                status.parentElement.className = 'test-section error';
                response.textContent = `Error: ${error.message}`;
            } finally {
                button.disabled = false;
            }
        }

        // Copy the API functions from popup.js for testing
        async function callVertexAI(content, contentType, retryCount = 0) {
            const maxRetries = 3;
            
            try {
                const apiUrl = `${CONFIG.VERTEX_AI_URL}/${CONFIG.MODEL_ID}:streamGenerateContent?key=${CONFIG.API_KEY}`;
                const requestBody = createVertexAIRequest(content, contentType);
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;
                
                if (!generatedText) {
                    throw new Error('No response text from Vertex AI');
                }

                // Parse JSON response
                const jsonMatch = generatedText.match(/\{[\s\S]*\}/);
                if (!jsonMatch) {
                    throw new Error('No valid JSON found in response');
                }

                const result = JSON.parse(jsonMatch[0]);
                return result;

            } catch (error) {
                console.error(`Vertex AI call failed (attempt ${retryCount + 1}):`, error);
                
                if (retryCount < maxRetries) {
                    console.log(`Retrying... (${retryCount + 1}/${maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
                    return callVertexAI(content, contentType, retryCount + 1);
                }
                
                throw error;
            }
        }

        function createVertexAIRequest(content, contentType) {
            const prompt = `
You are a fact-checking AI assistant. Analyze the provided ${contentType} and return a structured JSON response.

IMPORTANT INSTRUCTIONS:
- If analyzing an image, first examine the image and extract/parse any text visible in the image
- Always verify that any source URLs you provide are valid and accessible before including them in your response
- Base your fact-checking on reliable, verifiable sources

First, classify the content type as one of: "meme", "incomplete_sentence", "not_news", or "news".

If the content is "news", provide a verdict as one of: "real_text", "real_image", "false_text", or "false_image".
If the content is not news, set verdict to "not_applicable".

Provide a confidence score (0-100) for your assessment based on:
- Quality and reliability of available sources
- Clarity of the evidence
- Consensus among fact-checking organizations

Give a clear explanation of your reasoning, including:
- What specific claims were verified
- What evidence supports or contradicts the claims
- Any context that affects the interpretation

Provide a credible fact-checking source with a valid URL and name. Ensure the URL is accessible and relevant, recheck and revisit the url, if not found, search for other source, else return "not_applicable".

Return ONLY a valid JSON object with this exact structure:
{
  "content_type": "meme|incomplete_sentence|not_news|news",
  "verdict": "real_text|real_image|false_text|false_image|not_applicable",
  "confidence": 85,
  "explanation": "Your detailed explanation here",
  "source": {
    "url": "https://example.com",
    "name": "Source Name"
  }
}

${contentType === 'text' ? `Text to analyze: ${content}` : ''}
`;

            // Create request body based on content type
            if (contentType === 'image') {
                const imagePrompt = `${prompt}\n\nImage URL to analyze: ${content}`;
                
                return {
                    contents: {
                        role: "user",
                        parts: [
                            {
                                text: imagePrompt
                            }
                        ]
                    }
                };
            } else {
                return {
                    contents: {
                        role: "user",
                        parts: [
                            {
                                text: prompt
                            }
                        ]
                    }
                };
            }
        }
    </script>
</body>
</html>
